# Strapi Environment Configuration
# Generated for ONDC Seller Platform CMS

# Server Configuration
HOST=0.0.0.0
PORT=1337

# App Keys (Required for session middleware)
# These are randomly generated secure keys for production use
APP_KEYS=n5zNAhRgtYnUL/GiKsIqqseS/5i8BMw7q3zCOtt53GI=,T2E6kp2cqgq7l2wTEICwU1DwayKVmo9Uq/VA0ZzJZhg=,5HiyEX5CFzotZ7QFAq61XIgAtul4MJm0cuZOjFDOT4A=,d+0hKVwrm55JWnznZVFrmihkz7aceSTh5EJeMgED5is=

# API Token Salt (for API authentication)
API_TOKEN_SALT=AUzdqK+Wd7fj4y2upR3ZoCl3xxvezBIpKtst/mLtf94=

# Admin JWT Secret (for admin panel authentication)
ADMIN_JWT_SECRET=0WhLC/KVSHSuH2dwmrrNo9tswCdbfscjJsQL8WizZ6s=

# Transfer Token Salt (for data transfer operations)
TRANSFER_TOKEN_SALT=86cyxqYsJtdhdZbEFB3NotGLVbB8YqCmfRiKZAmp1t0=

# JWT Secret (for general JWT operations)
JWT_SECRET=RpC+Ninsqql4bsZlYRYUytT1HWyrC2McHmyFQ5cf0Us=

# Database Configuration
# Using PostgreSQL for production and better multi-tenant support
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=strapi_cms
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi_password
DATABASE_SSL=false

# File Upload Configuration
# Using local provider (files stored in public/uploads)
UPLOAD_PROVIDER=local

# Environment
NODE_ENV=development

# CORS Configuration
# Allow frontend to access the CMS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Security
# Disable telemetry for privacy
STRAPI_DISABLE_UPDATE_NOTIFICATION=true
STRAPI_TELEMETRY_DISABLED=true
