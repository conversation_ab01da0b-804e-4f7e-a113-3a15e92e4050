'use client';

import React, { useState } from 'react';
import { useStoreAuthContext } from './StoreAuthProvider';
import { storeAuthAPI, StoreLoginCredentials } from '@/lib/api/storeAuth';
import { useToast } from '@/app/providers/toast-provider';
import { useGlobalLoading } from '../loading/GlobalLoadingProvider';

interface StoreAuthModalProps {
  isOpen: boolean;
  mode: 'login' | 'signup';
  onClose: () => void;
  onSwitchMode: (mode: 'login' | 'signup') => void;
  storeHandle: string;
}

export const StoreAuthModal: React.FC<StoreAuthModalProps> = ({ 
  isOpen, 
  mode, 
  onClose, 
  onSwitchMode, 
  storeHandle 
}) => {
  const { showToast } = useToast();
  const { startLoading, stopLoading, updateMessage } = useGlobalLoading();

  const { login, error, clearError } = useStoreAuthContext();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    rememberMe: false,
    agreeToTerms: false,
    subscribeNewsletter: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    
    // Clear error when user starts typing
    if (error) {
      clearError();
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      phone: '',
      rememberMe: false,
      agreeToTerms: false,
      subscribeNewsletter: false,
    });
  };

  const validateSignupForm = () => {
    if (!formData.email || !formData.password || !formData.firstName || !formData.lastName) {
      throw new Error('Please fill in all required fields');
    }
    
    if (formData.password !== formData.confirmPassword) {
      throw new Error('Passwords do not match');
    }
    
    if (formData.password.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }
    
    if (!formData.agreeToTerms) {
      throw new Error('Please agree to the Terms of Service and Privacy Policy');
    }
  };

  const handleSignup = async () => {
    validateSignupForm();
    
    setIsLoading(true);
    setLoadingMessage('Creating your account...');
    
    const medusaUrl = process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 'http://localhost:9000';
    
    try {
      // Step 1: Register user with email and password
      setLoadingMessage('Registering user...');
      const registerResponse = await fetch(`${medusaUrl}/auth/customer/emailpass/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'x-tenant-id': storeHandle || 'default',
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        })
      });
      
      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        throw new Error(errorData.message || 'Registration failed');
      }
      
      const registerData = await registerResponse.json();
      const token = registerData.token;
      
      if (!token) {
        throw new Error('No token received from registration');
      }
      
      // Step 2: Create customer profile with additional details
      setLoadingMessage('Creating customer profile...');
      const customerResponse = await fetch(`${medusaUrl}/store/customers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
          'x-tenant-id': storeHandle || 'default',
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        },
        body: JSON.stringify({
          email: formData.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone: formData.phone || undefined,
          metadata: {
            user_type: 'customer'
          }
        })
      });
      
      if (!customerResponse.ok) {
        const errorData = await customerResponse.json();
        throw new Error(errorData.message || 'Customer profile creation failed');
      }
      
      // Step 3: Auto-login the user
      setLoadingMessage('Signing you in...');
      const credentials: StoreLoginCredentials = {
        email: formData.email,
        password: formData.password,
      };
      
      await login(credentials, storeHandle);
      
      showToast('Account created and logged in successfully!', 'success');
      
      // Close modal and reset form
      onClose();
      resetForm();
      
    } catch (error: any) {
      console.error('Signup error:', error);
      showToast(error.message || 'Signup failed. Please try again.', 'error');
      throw error; // Re-throw to be handled by the form submission
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (mode === 'login') {
        setLoadingMessage('Signing in...');
        
        // Start global loading for store login
        startLoading('page', 'Signing you in...', {
          subMessage: `Logging into ${storeHandle} store`,
          actionId: 'store-login'
        });
        
        const credentials: StoreLoginCredentials = {
          email: formData.email,
          password: formData.password,
        };
        
        updateMessage('Authenticating...', 'Verifying your store credentials');
        const resp = await login(credentials, storeHandle);
        console.log("Logged in ======<><><><>", resp);
        
        updateMessage('Login successful!', 'Preparing your store dashboard');
        showToast('User logged in successfully', 'success');
        
        // Close modal on success
        onClose();
        
        // Stop loading after a brief delay to show success message
        setTimeout(() => {
          stopLoading('store-login');
        }, 1000);
        
        return;
        
        // Reset form
        resetForm();
      } else {
        // Signup flow
        await handleSignup();
      }
    } catch (error) {
      // Error is handled by the auth context or signup function
      console.error('Store auth error:', error);
      
      // Stop loading on error
      if (mode === 'login') {
        stopLoading('store-login');
      }
    } finally {
      if (mode === 'login') {
        setIsLoading(false);
        setLoadingMessage('');
      }
    }
  };

  const switchMode = () => {
    clearError();
    onSwitchMode(mode === 'login' ? 'signup' : 'login');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ 
          backgroundColor: 'var(--theme-surface, #ffffff)',
          borderColor: 'var(--theme-border, #e5e7eb)',
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <h2 
            className="text-xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {mode === 'login' ? 'Welcome Back' : 'Create Account'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full transition-colors hover:opacity-80"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Error Message */}
          {error && (
            <div 
              className="p-3 rounded-lg border text-sm"
              style={{ 
                backgroundColor: '#fef2f2',
                borderColor: '#fecaca',
                color: '#dc2626',
              }}
            >
              {error}
            </div>
          )}

          {/* Loading Message */}
          {isLoading && loadingMessage && (
            <div 
              className="p-3 rounded-lg border text-sm flex items-center"
              style={{ 
                backgroundColor: '#f0f9ff',
                borderColor: '#bae6fd',
                color: '#0369a1',
              }}
            >
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
              {loadingMessage}
            </div>
          )}

          {/* Signup Fields */}
          {mode === 'signup' && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    First Name *
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="John"
                  />
                </div>
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Last Name *
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="Doe"
                  />
                </div>
              </div>
              
              {/* Phone Number */}
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="+919856985362"
                />
              </div>
            </>
          )}

          {/* Email */}
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
              }}
              placeholder="<EMAIL>"
            />
          </div>

          {/* Password */}
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Password *
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
              }}
              placeholder="••••••••"
            />
          </div>

          {/* Confirm Password (Signup only) */}
          {mode === 'signup' && (
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Confirm Password *
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                disabled={isLoading}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors disabled:opacity-50"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="••••••••"
              />
            </div>
          )}

          {/* Remember Me (Login only) */}
          {mode === 'login' && (
            <div className="flex items-center">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                disabled={isLoading}
                className="mr-2 disabled:opacity-50"
                style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
              />
              <label 
                className="text-sm"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Remember me
              </label>
            </div>
          )}

          {/* Terms Agreement (Signup only) */}
          {mode === 'signup' && (
            <div className="space-y-3">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="mr-2 mt-1 disabled:opacity-50"
                  style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
                />
                <label 
                  className="text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  I agree to the{' '}
                  <a 
                    href={`/${storeHandle}/terms-and-conditions`}
                    className="underline transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a 
                    href={`/${storeHandle}/privacy-policy`}
                    className="underline transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    Privacy Policy
                  </a>
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="subscribeNewsletter"
                  checked={formData.subscribeNewsletter}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="mr-2 disabled:opacity-50"
                  style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
                />
                <label 
                  className="text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Subscribe to newsletter for updates and offers
                </label>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center ${
              isLoading 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:shadow-lg'
            }`}
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            )}
            {isLoading 
              ? (loadingMessage || (mode === 'login' ? 'Signing In...' : 'Creating Account...'))
              : (mode === 'login' ? 'Sign In' : 'Create Account')
            }
          </button>

          {/* Switch Mode */}
          <div className="text-center">
            <p 
              className="text-sm"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {mode === 'login' ? "Don't have an account?" : 'Already have an account?'}{' '}
              <button
                type="button"
                onClick={switchMode}
                disabled={isLoading}
                className="font-medium transition-colors hover:opacity-80 disabled:opacity-50"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                {mode === 'login' ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>



          {/* Demo Credentials (Login only) */}
          {mode === 'login' && (
            <div 
              className="p-3 rounded-lg text-xs"
              style={{ 
                backgroundColor: 'var(--theme-background, #f9fafb)',
                color: 'var(--theme-text-secondary, #6b7280)',
              }}
            >
              <p className="font-medium mb-1">Demo Credentials:</p>
              <p>Email: <EMAIL></p>
              <p>Password: password123</p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};