'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaDashboardAnalytics } from '@/lib/api/medusa-admin';

// Material Design 3 Components
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Stack,
  Avatar,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Paper,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import {
  Inventory as ProductsIcon,
  Receipt as OrdersIcon,
  People as CustomersIcon,
  AttachMoney as RevenueIcon,
  Add as AddIcon,
  Category as CategoryIcon,
  LocalOffer as CouponIcon,
  Collections as CollectionIcon,
  Settings as SettingsIcon,
  FileUpload as ExportIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';

// Recharts components
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

interface StatCard {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ReactNode;
  href: string;
  color: string;
}

type TimePeriod = 'weekly' | 'monthly' | 'quarterly' | 'yearly';

interface DashboardData {
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  recentOrders: {
    id: string;
    customer: string;
    amount: string;
    status: string;
    date: string;
  }[];
  topProducts: {
    id: string;
    name: string;
    sales: number;
    revenue: number;
    status: string;
    stock?: number;
    price?: number;
    product_handle?: string;
  }[];
  chartData: {
    month: string;
    revenue: number;
    orders: number;
    customers: number;
  }[];
  areaChartData: {
    day: string;
    sales: number;
    profit: number;
  }[];
}

// Sample chart data - replace with real API data
const generateChartData = (period: TimePeriod) => {
  let labels: string[] = [];
  
  switch (period) {
    case 'weekly':
      labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      break;
    case 'monthly':
      labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      break;
    case 'quarterly':
      labels = ['Q1', 'Q2', 'Q3', 'Q4'];
      break;
    case 'yearly':
      const currentYear = new Date().getFullYear();
      labels = Array.from({ length: 5 }, (_, i) => (currentYear - 4 + i).toString());
      break;
  }
  
  return labels.map((label, index) => ({
    month: label,
    revenue: Math.floor(Math.random() * 50000) + 20000,
    orders: Math.floor(Math.random() * 200) + 50,
    customers: Math.floor(Math.random() * 100) + 30,
  }));
};

const generateAreaChartData = (period: TimePeriod) => {
  let labels: string[] = [];
  
  switch (period) {
    case 'weekly':
      labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      break;
    case 'monthly':
      labels = Array.from({ length: 30 }, (_, i) => `${i + 1}`);
      break;
    case 'quarterly':
      labels = Array.from({ length: 12 }, (_, i) => `Week ${i + 1}`);
      break;
    case 'yearly':
      labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      break;
  }
  
  return labels.map((label, index) => ({
    day: label,
    sales: Math.floor(Math.random() * 5000) + 1000,
    profit: Math.floor(Math.random() * 2000) + 500,
  }));
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Completed':
    case 'completed':
      return 'success';
    case 'Processing':
    case 'processing':
      return 'warning';
    case 'Shipped':
    case 'shipped':
      return 'info';
    case 'Pending':
    case 'pending':
      return 'default';
    default:
      return 'default';
  }
};

export const AdminDashboard: React.FC = () => {

  
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { user, getStoreHandle } = useAuthStore();
  
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    totalProducts: 0,
    totalOrders: 0,
    totalRevenue: 0,
    totalCustomers: 0,
    recentOrders: [],
    topProducts: [],
    chartData: [],
    areaChartData: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('monthly');
  const [storeData, setStoreData] = useState<any>(null);
  
  // Handle null user state during logout process
  const isUserNull = !user;
  
  // Extract user information with proper null checking
  const userName = user?.user ? `${user.user?.first_name || ''} ${user.user?.last_name || ''}`.trim() : 'Admin';
  const userEmail = user?.user?.email || '<EMAIL>';
  const dynamicStoreHandle = getStoreHandle() || storeHandle;
  
  // Extract store name from user data or use store handle as fallback
  const storeName = user?.user?.metadata?.store_name ||
                   user?.metadata?.store_name ||
                   (storeHandle ? storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Your Store');

  // Handle time period change
  const handleTimePeriodChange = (event: SelectChangeEvent<TimePeriod>) => {
    setTimePeriod(event.target.value as TimePeriod);
  };

  // Fetch store configuration data
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!dynamicStoreHandle) return;
      
      try {
        const storeConfig = await medusaAdminService.getStoreConfig(dynamicStoreHandle);
        setStoreData(storeConfig);
      } catch (error) {
        console.error('Error fetching store config:', error);
        setStoreData(null);
      }
    };

    fetchStoreData();
  }, [dynamicStoreHandle]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!dynamicStoreHandle) return;
      
      setIsLoading(true);
      try {
        // Fetch dashboard analytics from the new API with time period
        const analyticsData = await medusaAdminService.getDashboardAnalytics(dynamicStoreHandle, timePeriod);
        
        // Transform recent orders data (now using the normalized structure from API)
        const recentOrders = analyticsData.recent_orders?.map((order) => ({
          id: order.display_id || order.id,
          customer: order.customer_email || 'Guest Customer',
          amount: `₹${Number(order.total).toLocaleString()}`,
          status: order.status.toLowerCase(),
          date: new Date(order.created_at).toLocaleDateString(),
        })) || [];
        
        // Transform top products data (now using the normalized structure from API)
        const topProducts = analyticsData.top_products?.map((product) => ({
          id: product.id || 'N/A',
          name: product.title || 'Unknown Product',
          sales: Number(product.sales_count) || 0,
          revenue: Number(product.revenue) || 0,
          status: product.status || 'published',
          stock: Number(product.stock) || 0,
          price: Number(product.price) || 0,
          product_handle: product.product_handle || 'unknown'
        })) || [];
        
        // Use real chart data from API - no fallback to mock data
        const chartData = analyticsData.revenue_chart_data || [];
        const areaChartData = analyticsData.sales_chart_data || [];
        
        setDashboardData({
          totalProducts: Number(analyticsData.total_products) || 0,
          totalOrders: Number(analyticsData.total_orders) || 0,
          totalRevenue: Number(analyticsData.total_revenue) || 0,
          totalCustomers: Number(analyticsData.total_customers) || 0,
          recentOrders,
          topProducts,
          chartData,
          areaChartData,
        });
      } catch (error) {
        console.error('Error fetching dashboard analytics:', error);
        // Use empty data on error to show proper empty states
        setDashboardData({
          totalProducts: 0,
          totalOrders: 0,
          totalRevenue: 0,
          totalCustomers: 0,
          recentOrders: [],
          topProducts: [],
          chartData: [],
          areaChartData: [],
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [dynamicStoreHandle, timePeriod]);

  const stats: StatCard[] = [
    {
      title: 'Total Products',
      value: dashboardData.totalProducts.toLocaleString(),
      change: '+12%',
      changeType: 'increase',
      href: `/${dynamicStoreHandle}/admin/products`,
      icon: <ProductsIcon />,
      color: 'primary',
    },
    {
      title: 'Total Orders',
      value: dashboardData.totalOrders.toLocaleString(),
      change: '+8%',
      changeType: 'increase',
      href: `/${dynamicStoreHandle}/admin/orders`,
      icon: <OrdersIcon />,
      color: 'success',
    },
    {
      title: 'Total Revenue',
      value: `₹${dashboardData.totalRevenue.toLocaleString()}`,
      change: '+23%',
      changeType: 'increase',
      href: `/${dynamicStoreHandle}/admin/analytics`,
      icon: <RevenueIcon />,
      color: 'warning',
    },
    {
      title: 'Total Customers',
      value: dashboardData.totalCustomers.toLocaleString(),
      change: '+15%',
      changeType: 'increase',
      href: `/${dynamicStoreHandle}/admin/customers`,
      icon: <CustomersIcon />,
      color: 'info',
    },
  ];
  
  // Don't render content if user is null (during logout)
  if (isUserNull) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        {/* Empty content during logout */}
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Page Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Dashboard
          </Typography>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography variant="body1" color="text.secondary">
              Welcome back, {userName}! Here&apos;s what&apos;s happening with
            </Typography>
            {storeData?.logo && (
              <Box
                component="img"
                src={storeData.logo}
                alt={`${storeName} logo`}
                sx={{
                  height: 24,
                  width: 'auto',
                  maxWidth: 120,
                  objectFit: 'contain',
                  borderRadius: 1,
                }}
                onError={(e) => {
                  // Hide image if it fails to load
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <Typography variant="body1" color="text.secondary" fontWeight="medium">
              {storeName}
            </Typography>
          </Stack>
        </Box>
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="time-period-label">Time Period</InputLabel>
          <Select
            labelId="time-period-label"
            id="time-period-select"
            value={timePeriod}
            label="Time Period"
            onChange={handleTimePeriodChange}
          >
            <MenuItem value="weekly">Weekly</MenuItem>
            <MenuItem value="monthly">Monthly</MenuItem>
            <MenuItem value="quarterly">Quarterly</MenuItem>
            <MenuItem value="yearly">Yearly</MenuItem>
          </Select>
        </FormControl>
      </Stack>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat) => (
          <Grid item size={{xs:12, sm:6, lg:3}} key={stat.title}>
            <Card
              // component={Link}
              // href={stat.href}
           
            >
              <CardContent>
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" gutterBottom>
                      {isLoading ? <CircularProgress size={24} /> : stat.value}
                    </Typography>
                 
                  </Box>
                  <Box
                    sx={{
                      p: 1.5,
                      bgcolor: `${stat.color}.light`,
                      borderRadius: 2,
                      color: `${stat.color}.main`,
                    }}
                  >
                    {stat.icon}
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Line Chart */}
        <Grid item size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Revenue & Orders Trend
              </Typography>
              <Box sx={{ width: '100%', height: 300 }}>
                {dashboardData.chartData.length === 0 ? (
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center', 
                    justifyContent: 'center',
                    height: '100%',
                    color: 'text.secondary'
                  }}>
                    <TrendingUpIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                    <Typography variant="body1" color="text.secondary">
                      No revenue trend data available
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Chart will appear once you have sales data for the selected period
                    </Typography>
                  </Box>
                ) : (
                  <ResponsiveContainer>
                    <LineChart data={dashboardData.chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="revenue" 
                        stroke="#1976d2" 
                        strokeWidth={2}
                        name="Revenue (₹)"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="orders" 
                        stroke="#2e7d32" 
                        strokeWidth={2}
                        name="Orders"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Area Chart */}
        <Grid item size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Sales & Profit Analysis
              </Typography>
              <Box sx={{ width: '100%', height: 300 }}>
                {dashboardData.areaChartData.length === 0 ? (
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center', 
                    justifyContent: 'center',
                    height: '100%',
                    color: 'text.secondary'
                  }}>
                    <TrendingUpIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                    <Typography variant="body1" color="text.secondary">
                      No sales analysis data available
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Chart will appear once you have sales data for the selected period
                    </Typography>
                  </Box>
                ) : (
                  <ResponsiveContainer>
                    <AreaChart data={dashboardData.areaChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="sales" 
                        stackId="1"
                        stroke="#ff9800" 
                        fill="#ff9800"
                        fillOpacity={0.6}
                        name="Sales (₹)"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="profit" 
                        stackId="1"
                        stroke="#4caf50" 
                        fill="#4caf50"
                        fillOpacity={0.6}
                        name="Profit (₹)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Orders and Products Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Top Orders */}
        <Grid item size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                <Typography variant="h6" fontWeight="bold">
                  Top Orders
                </Typography>
                <Button
                  component={Link}
                  href={`/${dynamicStoreHandle}/admin/orders`}
                  size="small"
                  color="primary"
                >
                  View all
                </Button>
              </Stack>
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress size={32} />
                </Box>
              ) : dashboardData.recentOrders.length === 0 ? (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center', 
                  py: 4,
                  color: 'text.secondary'
                }}>
                  <OrdersIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                  <Typography variant="body1" color="text.secondary">
                    No recent orders
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Orders will appear here once customers start purchasing
                  </Typography>
                </Box>
              ) : (
                <Stack spacing={2}>
                  {dashboardData.recentOrders.map((order, index) => (
                    <Card key={order.id} variant="outlined" sx={{ 
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        boxShadow: 2,
                        transform: 'translateY(-1px)',
                      }
                    }}>
                      <CardContent sx={{ py: 2 }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Box sx={{ flex: 1 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: '50%',
                                  bgcolor: 'primary.light',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'primary.main',
                                  fontWeight: 'bold',
                                  fontSize: '0.875rem',
                                }}
                              >
                                #{index + 1}
                              </Box>
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  {`#od_${order.id}`}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {order.customer}
                                </Typography>
                              </Box>
                            </Stack>
                          </Box>
                          <Box sx={{ textAlign: 'right' }}>
                            <Typography variant="h6" fontWeight="bold" color="primary.main">
                              {order.amount}
                            </Typography>
                            <Chip
                              label={order.status}
                              size="small"
                              color={getStatusColor(order.status) as any}
                              sx={{ textTransform: 'capitalize', mt: 0.5 }}
                            />
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  ))}
                </Stack>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Top Products */}
        <Grid item size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                <Typography variant="h6" fontWeight="bold">
                  Top Products
                </Typography>
                <Button
                  component={Link}
                  href={`/${dynamicStoreHandle}/admin/products`}
                  size="small"
                  color="primary"
                >
                  View all
                </Button>
              </Stack>
              
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress size={32} />
                </Box>
              ) : dashboardData.topProducts.length === 0 ? (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center', 
                  py: 4,
                  color: 'text.secondary'
                }}>
                  <ProductsIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                  <Typography variant="body1" color="text.secondary">
                    No top products data available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Products will appear here once you have sales data
                  </Typography>
                </Box>
              ) : (
                <Stack spacing={2}>
                  {dashboardData.topProducts.map((product, index) => (
                    <Card key={product.id} variant="outlined" sx={{ 
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        boxShadow: 2,
                        transform: 'translateY(-1px)',
                      }
                    }}>
                      <CardContent sx={{ py: 2 }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Box sx={{ flex: 1 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: 2,
                                  bgcolor: 'success.light',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <ProductsIcon />
                              </Box>
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  {product.name}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {product.product_handle}
                                </Typography>
                              </Box>
                            </Stack>
                          </Box>
                          <Box sx={{ textAlign: 'right' }}>
                            <Typography variant="h6" fontWeight="bold" color="success.main">
                              ₹{(product.price || 0).toLocaleString()}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                            {product.stock} stock
                            </Typography>
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  ))}
                </Stack>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Quick Actions
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/products/new`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'primary.light',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'primary.main',
                  }}
                >
                  <AddIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  Add Product
                </Typography>
              </Button>
            </Grid>

            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/categories/new`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'success.light',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'success.main',
                  }}
                >
                  <CategoryIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  Add Category
                </Typography>
              </Button>
            </Grid>

            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/collections/new`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'warning.light',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'warning.main',
                  }}
                >
                  <CollectionIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  Add Collection
                </Typography>
              </Button>
            </Grid>

            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/orders`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'info.light',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'info.main',
                  }}
                >
                  <OrdersIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  View Orders
                </Typography>
              </Button>
            </Grid>

            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/customers`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'secondary.light',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'secondary.main',
                  }}
                >
                  <CustomersIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  View Customers
                </Typography>
              </Button>
            </Grid>

            <Grid item xs={6} sm={4} md={2}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/store-settings`}
                variant="outlined"
                fullWidth
                sx={{
                  flexDirection: 'column',
                  py: 2,
                  height: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'grey.200',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 1,
                    color: 'grey.600',
                  }}
                >
                  <SettingsIcon />
                </Box>
                <Typography variant="body2" fontWeight="medium">
                  Settings
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};