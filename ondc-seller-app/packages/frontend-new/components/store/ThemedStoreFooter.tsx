'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Store } from '@/types';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

interface ThemedStoreFooterProps {
  store: Store;
  storeHandle: string;
}

export const ThemedStoreFooter: React.FC<ThemedStoreFooterProps> = ({ store, storeHandle }) => {
  const { storeData } = useStoreConfigStore(storeHandle);
  const defaultLogo = '/images/default-store-logo.png';
  
  // Use dynamic data if available, fallback to props
  const displayName = storeData?.name || store.name || storeHandle;
  const displayLogo = storeData?.logo || store.logo || defaultLogo;
  const displayContact = storeData?.contact || store.contact_info;
  const displayAddress = storeData?.address || store.address;
  const displaySocialMedia = storeData?.social_media || store.social_links;

  const cmsPages = [
    { name: 'About Us', href: `/${storeHandle}/about-us` },
    { name: 'Contact Us', href: `/${storeHandle}/contact-us` },
    { name: 'Privacy Policy', href: `/${storeHandle}/privacy-policy` },
    { name: 'Terms & Conditions', href: `/${storeHandle}/terms-and-conditions` },
    { name: 'Refund Policy', href: `/${storeHandle}/refund-policy` },
    { name: 'Shipping Policy', href: `/${storeHandle}/shipping-policy` },
  ];

  const quickLinks = [
    { name: 'All Products', href: `/${storeHandle}/products` },
    { name: 'Categories', href: `/${storeHandle}/categories` },
    { name: 'Latest Arrivals', href: `/${storeHandle}/products?filter=latest` },
    { name: 'Top Selling', href: `/${storeHandle}/products?filter=bestsellers` },
    { name: 'Sale Items', href: `/${storeHandle}/products?filter=sale` },
    { name: 'Gift Cards', href: `/${storeHandle}/gift-cards` },
  ];

  const customerService = [
    { name: 'Help Center', href: `/${storeHandle}/help` },
    { name: 'Track Your Order', href: `/${storeHandle}/track-order` },
    { name: 'Returns & Exchanges', href: `/${storeHandle}/returns` },
    { name: 'Size Guide', href: `/${storeHandle}/size-guide` },
    { name: 'FAQ', href: `/${storeHandle}/faq` },
    { name: 'Live Chat', href: `/${storeHandle}/chat` },
  ];

  return (
    <footer 
      className="text-white themed-footer"
      style={{ backgroundColor: 'var(--theme-text)' }}
    >
      {/* Newsletter Signup */}
      <div 
        className="py-8 sm:py-12"
        style={{ backgroundColor: 'var(--theme-dark-muted)' }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold mb-2 text-white">Stay Updated</h3>
            <p className="mb-4 sm:mb-6 text-sm sm:text-base" style={{ color: 'var(--theme-background)' }}>
              Subscribe to get special offers, free giveaways, and updates.
            </p>
            <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-2 sm:gap-0">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg sm:rounded-l-lg sm:rounded-r-none focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
                style={{ 
                  backgroundColor: 'var(--theme-surface)',
                  color: 'var(--theme-text)',
                  '--tw-ring-color': 'var(--theme-primary)',
                } as React.CSSProperties}
              />
              <button 
                className="px-4 sm:px-6 py-3 rounded-lg sm:rounded-r-lg sm:rounded-l-none font-medium transition-all duration-200 hover:shadow-lg text-sm sm:text-base"
                style={{ 
                  backgroundColor: 'var(--btn-primary)',
                  color: 'var(--btn-text)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary)';
                }}
              >
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="py-8 sm:py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="g">
            {/* Store Information */}
            <div className="sm:col-span-2 lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div 
                  className="relative w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2"
                  style={{ borderColor: 'var(--theme-primary)' }}
                >
                  <Image
                    src={displayLogo}
                    alt={`${displayName} logo`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 32px, 40px"
                  />
                </div>
                <h3 className="text-lg sm:text-xl font-bold text-white">{displayName}</h3>
              </div>
              
              {/* Contact Information */}
              {displayContact && (
                <div className="space-y-2 text-sm">
                  {displayContact.email && (
                    <div className="flex items-center space-x-2">
                      <svg 
                        className="w-4 h-4 flex-shrink-0" 
                        fill="none" 
                        stroke="var(--theme-primary)" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <a 
                        href={`mailto:${displayContact.email}`} 
                        className="transition-colors hover:opacity-80 break-all"
                        style={{ color: 'var(--theme-background)' }}
                      >
                        {displayContact.email}
                      </a>
                    </div>
                  )}
                  {displayContact.phone && (
                    <div className="flex items-center space-x-2">
                      <svg 
                        className="w-4 h-4 flex-shrink-0" 
                        fill="none" 
                        stroke="var(--theme-primary)" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <a 
                        href={`tel:${displayContact.phone}`} 
                        className="transition-colors hover:opacity-80"
                        style={{ color: 'var(--theme-background)' }}
                      >
                        {displayContact.phone}
                      </a>
                    </div>
                  )}
                  {(displayContact.address || displayAddress) && (
                    <div className="flex items-start space-x-2">
                      <svg 
                        className="w-4 h-4 mt-0.5 flex-shrink-0" 
                        fill="none" 
                        stroke="var(--theme-primary)" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span style={{ color: 'var(--theme-background)' }} className="text-sm">
                        {displayContact.address || (
                          displayAddress && [
                            displayAddress.street,
                            [displayAddress.city, displayAddress.state, displayAddress.postal_code].filter(Boolean).join(', '),
                            displayAddress.country
                          ].filter(Boolean).join(', ')
                        )}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Social Media Links */}
              {displaySocialMedia && (
                <div className="flex space-x-4 mt-6">
                  {displaySocialMedia.facebook && (
                    <a 
                      href={displaySocialMedia.facebook} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="transition-colors hover:opacity-80"
                      style={{ color: 'var(--theme-primary)' }}
                      aria-label="Facebook"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                  {displaySocialMedia.instagram && (
                    <a 
                      href={displaySocialMedia.instagram} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="transition-colors hover:opacity-80"
                      style={{ color: 'var(--theme-primary)' }}
                      aria-label="Instagram"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"/>
                      </svg>
                    </a>
                  )}
                  {displaySocialMedia.twitter && (
                    <a 
                      href={displaySocialMedia.twitter} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="transition-colors hover:opacity-80"
                      style={{ color: 'var(--theme-primary)' }}
                      aria-label="Twitter"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  )}
                </div>
              )}
            </div>

            {/* Customer Service */}
           

            {/* Legal & Policies */}
            <div>
              <h3 className="text-base sm:text-lg font-semibold mb-4 text-white">Legal & Policies</h3>
              <ul className="space-y-2">
                {cmsPages.slice(0, 4).map((page) => (
                  <li key={page.name}>
                    <Link 
                      href={page.href} 
                      className="text-sm transition-colors hover:opacity-80"
                      style={{ color: 'var(--theme-background)' }}
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
                {/* Show remaining links on larger screens */}
                <div className="hidden lg:block">
                  {cmsPages.slice(4).map((page) => (
                    <li key={page.name}>
                      <Link 
                        href={page.href} 
                        className="text-sm transition-colors hover:opacity-80"
                        style={{ color: 'var(--theme-background)' }}
                      >
                        {page.name}
                      </Link>
                    </li>
                  ))}
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div 
        className="py-4 sm:py-6 border-t"
        style={{ borderTopColor: 'var(--theme-primary)' }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div 
              className="text-xs sm:text-sm text-center sm:text-left"
              style={{ color: 'var(--theme-background)' }}
            >
              © {new Date().getFullYear()} {displayName}. All rights reserved.
            </div>
            <div 
              className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-xs sm:text-sm"
              style={{ color: 'var(--theme-background)' }}
            >
              <span className="text-center sm:text-left">Powered by Multi-Tenant E-commerce</span>
              <div className="flex items-center space-x-2">
                <span>We accept:</span>
                <div className="flex space-x-1 sm:space-x-2">
                  <div 
                    className="w-6 h-4 sm:w-8 sm:h-5 rounded text-xs flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: 'var(--theme-primary)' }}
                  >
                    VISA
                  </div>
                  <div 
                    className="w-6 h-4 sm:w-8 sm:h-5 rounded text-xs flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: 'var(--theme-accent)' }}
                  >
                    MC
                  </div>
                  <div 
                    className="w-6 h-4 sm:w-8 sm:h-5 rounded text-xs flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: 'var(--theme-secondary)' }}
                  >
                    PP
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};