import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { Typography, Box, Grid, Card, CardContent, Avatar, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { 
  LocalShipping, 
  Schedule, 
  Public, 
  AttachMoney,
  FlightTakeoff,
  DirectionsCar,
  Speed,
  Security
} from '@mui/icons-material';

interface ShippingPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function ShippingPolicyPage({ params }: ShippingPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="Shipping Policy" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Shipping Policy
      </Typography>
      
      <Box className="info-box">
        <Typography variant="body1">
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        At {storeName}, we are committed to delivering your orders quickly and safely. This Shipping 
        Policy outlines our shipping methods, delivery times, costs, and terms. Please review this 
        information before placing your order.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 3 }} fontWeight={600}>
        Shipping Overview
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <Schedule />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Processing Time
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Orders are processed within 1-2 business days (Monday-Friday, excluding holidays).
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <LocalShipping />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Free Shipping
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Free standard shipping on orders over $50 within the continental United States.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <Public />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  International Shipping
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We ship to over 50 countries worldwide with competitive international rates.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <Security />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Tracking Included
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                All shipments include tracking information sent to your email address.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Domestic Shipping (United States)
      </Typography>
      
      <Typography variant="body1" paragraph>
        We offer several shipping options for domestic orders within the United States:
      </Typography>

      <TableContainer component={Paper} elevation={1} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Shipping Method</strong></TableCell>
              <TableCell><strong>Delivery Time</strong></TableCell>
              <TableCell><strong>Cost</strong></TableCell>
              <TableCell><strong>Tracking</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <DirectionsCar sx={{ mr: 1, color: 'primary.main' }} />
                  Standard Shipping
                </Box>
              </TableCell>
              <TableCell>5-7 business days</TableCell>
              <TableCell>$5.99 (Free on orders $50+)</TableCell>
              <TableCell>✓ Included</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <Speed sx={{ mr: 1, color: 'warning.main' }} />
                  Expedited Shipping
                </Box>
              </TableCell>
              <TableCell>3-4 business days</TableCell>
              <TableCell>$12.99</TableCell>
              <TableCell>✓ Included</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <FlightTakeoff sx={{ mr: 1, color: 'error.main' }} />
                  Express Shipping
                </Box>
              </TableCell>
              <TableCell>1-2 business days</TableCell>
              <TableCell>$24.99</TableCell>
              <TableCell>✓ Included</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                <Box display="flex" alignItems="center">
                  <FlightTakeoff sx={{ mr: 1, color: 'error.dark' }} />
                  Overnight Shipping
                </Box>
              </TableCell>
              <TableCell>Next business day</TableCell>
              <TableCell>$39.99</TableCell>
              <TableCell>✓ Included</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        International Shipping
      </Typography>
      
      <Typography variant="body1" paragraph>
        We proudly ship to customers worldwide. International shipping rates and delivery times 
        vary by destination and are calculated at checkout based on your location and order weight.
      </Typography>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        International Shipping Zones
      </Typography>

      <TableContainer component={Paper} elevation={1} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Zone</strong></TableCell>
              <TableCell><strong>Countries</strong></TableCell>
              <TableCell><strong>Delivery Time</strong></TableCell>
              <TableCell><strong>Starting Cost</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>Zone 1</TableCell>
              <TableCell>Canada, Mexico</TableCell>
              <TableCell>7-14 business days</TableCell>
              <TableCell>$15.99</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Zone 2</TableCell>
              <TableCell>Europe, UK, Australia</TableCell>
              <TableCell>10-21 business days</TableCell>
              <TableCell>$24.99</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Zone 3</TableCell>
              <TableCell>Asia, South America</TableCell>
              <TableCell>14-28 business days</TableCell>
              <TableCell>$29.99</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Zone 4</TableCell>
              <TableCell>Africa, Middle East</TableCell>
              <TableCell>21-35 business days</TableCell>
              <TableCell>$34.99</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <Box className="warning-box">
        <Typography variant="h6" gutterBottom fontWeight={600}>
          Important International Shipping Notes
        </Typography>
        <ul>
          <li>
            <Typography variant="body1" component="span">
              <strong>Customs and Duties:</strong> International customers are responsible for any 
              customs duties, taxes, or fees imposed by their country.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Restricted Items:</strong> Some products may not be available for international 
              shipping due to regulations.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Address Accuracy:</strong> Please ensure your shipping address is complete and 
              accurate to avoid delays.
            </Typography>
          </li>
        </ul>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Order Processing
      </Typography>
      
      <Typography variant="body1" paragraph>
        Orders are processed Monday through Friday, excluding holidays. Orders placed after 2:00 PM EST 
        will be processed the next business day.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Processing Timeline
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>In-Stock Items:</strong> 1-2 business days
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Pre-Order Items:</strong> Ships on or before the estimated release date
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Custom/Personalized Items:</strong> 3-5 business days
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Large/Oversized Items:</strong> 2-3 business days
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Shipping Restrictions
      </Typography>
      
      <Typography variant="body1" paragraph>
        Due to shipping regulations and safety requirements, we cannot ship certain items to specific 
        locations:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Hazardous materials (batteries, chemicals, flammable items)
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Oversized items to P.O. Boxes or APO/FPO addresses
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Certain electronics to international destinations
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Perishable items during extreme weather conditions
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Delivery and Tracking
      </Typography>
      
      <Typography variant="body1" paragraph>
        Once your order ships, you'll receive a shipping confirmation email with tracking information. 
        You can track your package using the provided tracking number on our website or the carrier's website.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Delivery Requirements
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Signature Required:</strong> Orders over $200 require adult signature upon delivery
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Safe Delivery:</strong> Packages may be left at your door if the area is secure
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Delivery Attempts:</strong> Carriers typically make 3 delivery attempts before 
            returning to depot
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Shipping Delays
      </Typography>
      
      <Typography variant="body1" paragraph>
        While we strive to meet all delivery estimates, shipping times are not guaranteed and may be 
        affected by factors beyond our control:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Weather conditions and natural disasters
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Carrier delays or service disruptions
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            High shipping volumes during peak seasons
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Incorrect or incomplete shipping addresses
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Customs processing for international orders
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Lost or Damaged Packages
      </Typography>
      
      <Typography variant="body1" paragraph>
        If your package is lost or damaged during shipping, please contact us immediately at 
        shipping@{storeHandle}.com. We will work with the carrier to resolve the issue and ensure 
        you receive your order.
      </Typography>

      <Box className="highlight-box" sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom fontWeight={600}>
          Package Protection
        </Typography>
        <Typography variant="body1">
          All packages are insured for their full value. If your package is confirmed lost or damaged 
          by the carrier, we will send a replacement at no additional cost or provide a full refund.
        </Typography>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Address Changes
      </Typography>
      
      <Typography variant="body1" paragraph>
        Once an order is placed, shipping address changes may not be possible if the order has already 
        been processed. Please contact us immediately at support@{storeHandle}.com if you need to 
        change your shipping address.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Contact Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        For shipping-related questions or concerns, please contact us:
      </Typography>
      
      <Box sx={{ ml: 2 }}>
        <Typography variant="body1" paragraph>
          <strong>Shipping Support:</strong> shipping@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </Typography>
      </Box>

      <Box className="info-box" sx={{ mt: 4 }}>
        <Typography variant="body1">
          <strong>Note:</strong> This shipping policy is subject to change without notice. Any changes 
          will be posted on this page with an updated effective date. Shipping rates and delivery 
          times are estimates and may vary based on carrier performance and external factors.
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ShippingPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Shipping Policy | ${storeName}`,
    description: `Learn about ${storeName}'s shipping policy including delivery times, shipping costs, international shipping, and tracking information.`,
    keywords: 'shipping policy, delivery, shipping costs, international shipping, tracking, shipping times',
  };
}