import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { Typography, Box, Divider } from '@mui/material';

interface TermsAndConditionsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function TermsAndConditionsPage({ params }: TermsAndConditionsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="Terms and Conditions" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Terms and Conditions
      </Typography>
      
      <Box className="info-box">
        <Typography variant="body1">
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        Welcome to {storeName}. These Terms and Conditions ("Terms") govern your use of our website 
        and services. By accessing or using our website, you agree to be bound by these Terms. 
        If you do not agree with any part of these Terms, please do not use our services.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        1. Acceptance of Terms
      </Typography>
      
      <Typography variant="body1" paragraph>
        By accessing and using this website, you accept and agree to be bound by the terms and 
        provision of this agreement. Additionally, when using this website's particular services, 
        you shall be subject to any posted guidelines or rules applicable to such services.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        2. Use License
      </Typography>
      
      <Typography variant="body1" paragraph>
        Permission is granted to temporarily download one copy of the materials on {storeName}'s 
        website for personal, non-commercial transitory viewing only. This is the grant of a license, 
        not a transfer of title, and under this license you may not:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            modify or copy the materials
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            use the materials for any commercial purpose or for any public display (commercial or non-commercial)
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            attempt to decompile or reverse engineer any software contained on the website
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            remove any copyright or other proprietary notations from the materials
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        3. User Accounts
      </Typography>
      
      <Typography variant="body1" paragraph>
        When you create an account with us, you must provide information that is accurate, complete, 
        and current at all times. You are responsible for safeguarding the password and for all 
        activities that occur under your account.
      </Typography>
      
      <Typography variant="body1" paragraph>
        You agree not to disclose your password to any third party and to take sole responsibility 
        for activities and actions under your password, whether or not you have authorized such activities.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        4. Product Information and Pricing
      </Typography>
      
      <Typography variant="body1" paragraph>
        We strive to provide accurate product descriptions and pricing information. However, we do not 
        warrant that product descriptions, pricing, or other content is accurate, complete, reliable, 
        current, or error-free.
      </Typography>
      
      <Typography variant="body1" paragraph>
        We reserve the right to:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Modify or discontinue products without prior notice
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Correct pricing errors, even after an order has been placed
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Limit quantities available for purchase
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        5. Orders and Payment
      </Typography>
      
      <Typography variant="body1" paragraph>
        All orders are subject to acceptance and availability. We reserve the right to refuse or 
        cancel any order for any reason, including but not limited to product availability, errors 
        in product or pricing information, or problems identified by our fraud detection systems.
      </Typography>
      
      <Typography variant="body1" paragraph>
        Payment must be received by us before we ship any products. We accept various payment methods 
        as indicated on our website. All payments are processed securely through our payment partners.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        6. Shipping and Delivery
      </Typography>
      
      <Typography variant="body1" paragraph>
        We will make every effort to ship products within the timeframes specified on our website. 
        However, delivery dates are estimates and we are not responsible for delays caused by shipping 
        carriers or circumstances beyond our control.
      </Typography>
      
      <Typography variant="body1" paragraph>
        Risk of loss and title for products pass to you upon delivery to the shipping carrier.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        7. Returns and Refunds
      </Typography>
      
      <Typography variant="body1" paragraph>
        Our return and refund policy is detailed in our separate Refund Policy document. By making 
        a purchase, you agree to the terms outlined in our Refund Policy.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        8. Prohibited Uses
      </Typography>
      
      <Typography variant="body1" paragraph>
        You may not use our website for any unlawful purpose or to solicit others to perform unlawful acts. 
        You may not violate any local, state, national, or international law or regulation.
      </Typography>
      
      <Typography variant="body1" paragraph>
        Prohibited activities include, but are not limited to:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Transmitting or uploading viruses or malicious code
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Attempting to gain unauthorized access to our systems
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Using our website to spam or send unsolicited communications
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Impersonating another person or entity
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        9. Intellectual Property
      </Typography>
      
      <Typography variant="body1" paragraph>
        The content, organization, graphics, design, compilation, magnetic translation, digital 
        conversion, and other matters related to the website are protected under applicable copyrights, 
        trademarks, and other proprietary rights.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        10. Disclaimer of Warranties
      </Typography>
      
      <Box className="warning-box">
        <Typography variant="body1" paragraph>
          THE MATERIALS ON {storeName.toUpperCase()}'S WEBSITE ARE PROVIDED ON AN 'AS IS' BASIS. 
          {storeName.toUpperCase()} MAKES NO WARRANTIES, EXPRESSED OR IMPLIED, AND HEREBY DISCLAIMS 
          AND NEGATES ALL OTHER WARRANTIES INCLUDING WITHOUT LIMITATION, IMPLIED WARRANTIES OR 
          CONDITIONS OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT 
          OF INTELLECTUAL PROPERTY OR OTHER VIOLATION OF RIGHTS.
        </Typography>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        11. Limitation of Liability
      </Typography>
      
      <Typography variant="body1" paragraph>
        In no event shall {storeName} or its suppliers be liable for any damages (including, without 
        limitation, damages for loss of data or profit, or due to business interruption) arising out 
        of the use or inability to use the materials on {storeName}'s website, even if {storeName} 
        or an authorized representative has been notified orally or in writing of the possibility of such damage.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        12. Privacy Policy
      </Typography>
      
      <Typography variant="body1" paragraph>
        Your privacy is important to us. Please review our Privacy Policy, which also governs your 
        use of the website, to understand our practices.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        13. Changes to Terms
      </Typography>
      
      <Typography variant="body1" paragraph>
        We reserve the right to modify these Terms at any time. Changes will be effective immediately 
        upon posting on the website. Your continued use of the website after any changes constitutes 
        acceptance of the new Terms.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        14. Governing Law
      </Typography>
      
      <Typography variant="body1" paragraph>
        These Terms shall be governed by and construed in accordance with the laws of the jurisdiction 
        in which {storeName} operates, without regard to its conflict of law provisions.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        15. Contact Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you have any questions about these Terms and Conditions, please contact us at:
      </Typography>
      
      <Box sx={{ ml: 2 }}>
        <Typography variant="body1" paragraph>
          <strong>Email:</strong> legal@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Address:</strong> 123 Commerce Street, Business District, City, State 12345
        </Typography>
      </Box>

      <Divider sx={{ my: 4 }} />

      <Box className="highlight-box">
        <Typography variant="body1">
          <strong>Important:</strong> These terms and conditions constitute the entire agreement 
          between you and {storeName} regarding the use of our website and services. If any provision 
          of these Terms is found to be invalid or unenforceable, the remaining provisions will remain in full force and effect.
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: TermsAndConditionsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Terms and Conditions | ${storeName}`,
    description: `Read the terms and conditions for ${storeName}. Understand your rights and responsibilities when using our website and services.`,
    keywords: 'terms and conditions, terms of service, legal, user agreement, website terms',
  };
}