import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { Typography, Box, Grid, Card, CardContent, Avatar } from '@mui/material';
import { 
  Security, 
  Cookie, 
  Share, 
  ContactMail,
  Shield,
  Visibility,
  Lock,
  Settings
} from '@mui/icons-material';

interface PrivacyPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function PrivacyPolicyPage({ params }: PrivacyPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="Privacy Policy" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Privacy Policy
      </Typography>
      
      <Box className="info-box">
        <Typography variant="body1">
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        At {storeName}, we are committed to protecting your privacy and ensuring the security of your 
        personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard 
        your information when you visit our website and use our services.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 3 }} fontWeight={600}>
        Privacy Principles
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <Security />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Data Security
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We use industry-standard security measures to protect your personal information.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <Visibility />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Transparency
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We are transparent about what data we collect and how we use it.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <Settings />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  User Control
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                You have control over your personal information and privacy settings.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <Shield />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Minimal Collection
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We only collect information necessary to provide our services.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Information We Collect
      </Typography>
      
      <Typography variant="body1" paragraph>
        We collect information you provide directly to us, information we obtain automatically when 
        you use our services, and information from third-party sources.
      </Typography>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Personal Information You Provide
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Account Information:</strong> Name, email address, phone number, and password when you create an account
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Order Information:</strong> Billing and shipping addresses, payment information, and order history
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Communication:</strong> Information you provide when contacting customer service or participating in surveys
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Reviews and Feedback:</strong> Product reviews, ratings, and other user-generated content
          </Typography>
        </li>
      </ul>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Information Collected Automatically
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Device Information:</strong> IP address, browser type, operating system, and device identifiers
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Usage Information:</strong> Pages visited, time spent on site, click patterns, and referral sources
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Location Information:</strong> General geographic location based on IP address
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Cookies and Tracking:</strong> Information collected through cookies, web beacons, and similar technologies
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        How We Use Your Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        We use the information we collect for various purposes, including:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Service Provision:</strong> Processing orders, managing accounts, and providing customer support
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Communication:</strong> Sending order confirmations, shipping updates, and promotional emails
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Personalization:</strong> Customizing your shopping experience and recommending products
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Analytics:</strong> Understanding how our website is used to improve our services
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Security:</strong> Detecting and preventing fraud, abuse, and security incidents
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Legal Compliance:</strong> Complying with applicable laws and regulations
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Information Sharing and Disclosure
      </Typography>
      
      <Typography variant="body1" paragraph>
        We do not sell, trade, or rent your personal information to third parties. We may share your 
        information in the following circumstances:
      </Typography>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Service Providers
      </Typography>
      
      <Typography variant="body1" paragraph>
        We may share information with trusted third-party service providers who assist us in operating 
        our website and conducting our business, including:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Payment processors for secure transaction processing
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Shipping companies for order fulfillment
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Email service providers for communications
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Analytics providers for website performance analysis
          </Typography>
        </li>
      </ul>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Legal Requirements
      </Typography>
      
      <Typography variant="body1" paragraph>
        We may disclose your information if required by law or in response to valid requests by public 
        authorities, such as a court order or subpoena.
      </Typography>

      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Business Transfers
      </Typography>
      
      <Typography variant="body1" paragraph>
        In the event of a merger, acquisition, or sale of assets, your information may be transferred 
        as part of the transaction, subject to appropriate confidentiality protections.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Cookies and Tracking Technologies
      </Typography>
      
      <Typography variant="body1" paragraph>
        We use cookies and similar tracking technologies to enhance your browsing experience and 
        analyze website usage.
      </Typography>

      <Box className="highlight-box" sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom fontWeight={600}>
          Types of Cookies We Use
        </Typography>
        <ul>
          <li>
            <Typography variant="body1" component="span">
              <strong>Essential Cookies:</strong> Required for basic website functionality
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Performance Cookies:</strong> Help us understand how visitors interact with our website
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Functional Cookies:</strong> Remember your preferences and settings
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Marketing Cookies:</strong> Used to deliver relevant advertisements
            </Typography>
          </li>
        </ul>
      </Box>

      <Typography variant="body1" paragraph sx={{ mt: 2 }}>
        You can control cookies through your browser settings. However, disabling certain cookies 
        may affect the functionality of our website.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Data Security
      </Typography>
      
      <Typography variant="body1" paragraph>
        We implement appropriate technical and organizational security measures to protect your 
        personal information against unauthorized access, alteration, disclosure, or destruction.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }} fontWeight={600}>
        Security Measures Include
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            SSL encryption for data transmission
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Secure payment processing through PCI-compliant providers
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Regular security audits and vulnerability assessments
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Access controls and employee training on data protection
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Regular backups and disaster recovery procedures
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Your Privacy Rights
      </Typography>
      
      <Typography variant="body1" paragraph>
        Depending on your location, you may have certain rights regarding your personal information:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Access:</strong> Request access to your personal information we hold
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Correction:</strong> Request correction of inaccurate or incomplete information
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Deletion:</strong> Request deletion of your personal information
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Portability:</strong> Request a copy of your data in a portable format
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Opt-out:</strong> Unsubscribe from marketing communications
          </Typography>
        </li>
      </ul>

      <Typography variant="body1" paragraph>
        To exercise these rights, please contact us at privacy@{storeHandle}.com.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Data Retention
      </Typography>
      
      <Typography variant="body1" paragraph>
        We retain your personal information for as long as necessary to fulfill the purposes outlined 
        in this Privacy Policy, unless a longer retention period is required or permitted by law.
      </Typography>
      
      <Typography variant="body1" paragraph>
        When we no longer need your personal information, we will securely delete or anonymize it.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Children's Privacy
      </Typography>
      
      <Typography variant="body1" paragraph>
        Our services are not intended for children under 13 years of age. We do not knowingly collect 
        personal information from children under 13. If we become aware that we have collected personal 
        information from a child under 13, we will take steps to delete such information.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        International Data Transfers
      </Typography>
      
      <Typography variant="body1" paragraph>
        Your information may be transferred to and processed in countries other than your own. We ensure 
        that such transfers are conducted in accordance with applicable data protection laws and with 
        appropriate safeguards in place.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Third-Party Links
      </Typography>
      
      <Typography variant="body1" paragraph>
        Our website may contain links to third-party websites. We are not responsible for the privacy 
        practices of these external sites. We encourage you to review the privacy policies of any 
        third-party websites you visit.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Changes to This Privacy Policy
      </Typography>
      
      <Typography variant="body1" paragraph>
        We may update this Privacy Policy from time to time to reflect changes in our practices or 
        applicable laws. We will notify you of any material changes by posting the updated policy on 
        our website and updating the "Last Updated" date.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Contact Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you have any questions, concerns, or requests regarding this Privacy Policy or our data 
        practices, please contact us:
      </Typography>
      
      <Box sx={{ ml: 2 }}>
        <Typography variant="body1" paragraph>
          <strong>Privacy Officer:</strong> privacy@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Mailing Address:</strong><br />
          {storeName}<br />
          Attn: Privacy Officer<br />
          123 Commerce Street<br />
          Business District, City, State 12345
        </Typography>
      </Box>

      <Box className="warning-box" sx={{ mt: 4 }}>
        <Typography variant="body1">
          <strong>California Residents:</strong> If you are a California resident, you may have additional 
          rights under the California Consumer Privacy Act (CCPA). Please contact us for more information 
          about your rights and how to exercise them.
        </Typography>
      </Box>

      <Box className="info-box" sx={{ mt: 3 }}>
        <Typography variant="body1">
          <strong>Note:</strong> This Privacy Policy is effective as of the date listed above and applies 
          to all information collected by {storeName}. Your continued use of our services after any 
          changes to this Privacy Policy constitutes acceptance of those changes.
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PrivacyPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Privacy Policy | ${storeName}`,
    description: `Read ${storeName}'s privacy policy to understand how we collect, use, and protect your personal information when you use our services.`,
    keywords: 'privacy policy, data protection, personal information, cookies, data security, privacy rights',
  };
}