import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { Typography, Box, Grid, Card, CardContent, Avatar } from '@mui/material';
import { Store, People, LocalShipping, Security } from '@mui/icons-material';

interface AboutUsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function AboutUsPage({ params }: AboutUsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="About Us" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Welcome to {storeName}
      </Typography>
      
      <Typography variant="body1" paragraph>
        At {storeName}, we are passionate about providing our customers with the highest quality products 
        and exceptional service. Founded with a vision to make online shopping convenient, reliable, and 
        enjoyable, we have grown to become a trusted destination for customers seeking quality and value.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Our Story
      </Typography>
      
      <Typography variant="body1" paragraph>
        {storeName} was established in 2020 with a simple mission: to bridge the gap between quality 
        products and customer satisfaction. What started as a small venture has evolved into a 
        comprehensive online marketplace, serving thousands of satisfied customers across the region.
      </Typography>

      <Typography variant="body1" paragraph>
        Our journey began when our founders recognized the need for a reliable online platform that 
        prioritizes customer experience while offering competitive prices. Today, we continue to uphold 
        these values while expanding our product range and improving our services.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 3 }} fontWeight={600}>
        What We Offer
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <Store />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Quality Products
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We carefully curate our product selection to ensure that every item meets our high 
                standards for quality, durability, and value.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <People />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Customer Service
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Our dedicated customer service team is always ready to assist you with any questions 
                or concerns you may have.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <LocalShipping />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Fast Delivery
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We partner with reliable shipping providers to ensure your orders reach you quickly 
                and safely.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <Security />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Secure Shopping
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Your privacy and security are our top priorities. We use advanced encryption to 
                protect your personal and payment information.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Our Mission
      </Typography>
      
      <Box className="highlight-box">
        <Typography variant="body1" fontWeight={500}>
          "To provide an exceptional online shopping experience by offering quality products, 
          competitive prices, and outstanding customer service while building lasting relationships 
          with our customers."
        </Typography>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Our Values
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            <strong>Customer First:</strong> We prioritize our customers' needs and satisfaction above all else.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Quality Assurance:</strong> We maintain strict quality standards for all our products.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Transparency:</strong> We believe in honest communication and transparent business practices.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Innovation:</strong> We continuously improve our services and embrace new technologies.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Sustainability:</strong> We are committed to environmentally responsible business practices.
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Contact Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        We'd love to hear from you! Whether you have questions about our products, need assistance 
        with an order, or simply want to share your feedback, our team is here to help.
      </Typography>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="body1" paragraph>
          <strong>Email:</strong> support@{storeHandle}.com
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Phone:</strong> +****************
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </Typography>
      </Box>

      <Box className="info-box" sx={{ mt: 4 }}>
        <Typography variant="body1">
          Thank you for choosing {storeName}. We look forward to serving you and providing you with 
          an exceptional shopping experience!
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: AboutUsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `About Us | ${storeName}`,
    description: `Learn more about ${storeName}, our story, mission, values, and commitment to providing quality products and exceptional customer service.`,
    keywords: 'about us, company story, mission, values, customer service',
  };
}