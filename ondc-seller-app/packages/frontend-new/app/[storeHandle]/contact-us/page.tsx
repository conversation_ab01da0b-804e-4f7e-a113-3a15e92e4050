import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  Avatar,
  TextField,
  Button,
  Divider
} from '@mui/material';
import { 
  Email, 
  Phone, 
  LocationOn, 
  AccessTime,
  Send,
  Support,
  QuestionAnswer,
  ShoppingCart
} from '@mui/icons-material';

interface ContactUsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function ContactUsPage({ params }: ContactUsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="Contact Us" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Get in Touch with {storeName}
      </Typography>
      
      <Typography variant="body1" paragraph>
        We're here to help! Whether you have questions about our products, need assistance with an order, 
        or want to provide feedback, our customer service team is ready to assist you. Choose the method 
        that works best for you.
      </Typography>

      <Grid container spacing={4} sx={{ mt: 2 }}>
        {/* Contact Information */}
        <Grid item xs={12} md={6}>
          <Typography variant="h5" gutterBottom fontWeight={600}>
            Contact Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Card elevation={2}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <Email />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight={600}>
                        Email Support
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        support@{storeHandle}.com
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2">
                    For general inquiries, order support, and product questions.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12}>
              <Card elevation={2}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <Phone />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight={600}>
                        Phone Support
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        +****************
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2">
                    Speak directly with our customer service representatives.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12}>
              <Card elevation={2}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                      <LocationOn />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight={600}>
                        Business Address
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        123 Commerce Street<br />
                        Business District<br />
                        City, State 12345
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2">
                    Our main office and customer service center.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12}>
              <Card elevation={2}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                      <AccessTime />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight={600}>
                        Business Hours
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Monday - Friday: 9:00 AM - 6:00 PM (EST)<br />
                        Saturday: 10:00 AM - 4:00 PM (EST)<br />
                        Sunday: Closed
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2">
                    Our customer service team is available during these hours.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Contact Form */}
        <Grid item xs={12} md={6}>
          <Typography variant="h5" gutterBottom fontWeight={600}>
            Send Us a Message
          </Typography>
          
          <Card elevation={2}>
            <CardContent>
              <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  fullWidth
                  label="Your Name"
                  variant="outlined"
                  required
                />
                
                <TextField
                  fullWidth
                  label="Email Address"
                  type="email"
                  variant="outlined"
                  required
                />
                
                <TextField
                  fullWidth
                  label="Phone Number (Optional)"
                  type="tel"
                  variant="outlined"
                />
                
                <TextField
                  fullWidth
                  label="Order Number (If applicable)"
                  variant="outlined"
                />
                
                <TextField
                  fullWidth
                  label="Subject"
                  variant="outlined"
                  required
                />
                
                <TextField
                  fullWidth
                  label="Message"
                  multiline
                  rows={4}
                  variant="outlined"
                  required
                  placeholder="Please describe your inquiry or concern in detail..."
                />
                
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Send />}
                  sx={{ mt: 2 }}
                >
                  Send Message
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      <Typography variant="h5" gutterBottom fontWeight={600}>
        Frequently Asked Questions
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 1 }}>
        <Grid item xs={12} md={4}>
          <Card elevation={1}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2, width: 32, height: 32 }}>
                  <ShoppingCart fontSize="small" />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Order Issues
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Questions about order status, shipping, returns, or exchanges.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card elevation={1}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2, width: 32, height: 32 }}>
                  <Support fontSize="small" />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Product Support
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Information about product specifications, compatibility, or usage.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card elevation={1}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2, width: 32, height: 32 }}>
                  <QuestionAnswer fontSize="small" />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  General Inquiries
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Account questions, payment issues, or general store information.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box className="highlight-box" sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom fontWeight={600}>
          Response Time
        </Typography>
        <Typography variant="body1">
          We strive to respond to all inquiries within 24 hours during business days. For urgent 
          matters, please call our phone support line during business hours for immediate assistance.
        </Typography>
      </Box>

      <Box className="info-box" sx={{ mt: 3 }}>
        <Typography variant="body1">
          <strong>Note:</strong> For order-related inquiries, please have your order number ready 
          to help us assist you more efficiently. You can find your order number in your confirmation 
          email or account dashboard.
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ContactUsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Contact Us | ${storeName}`,
    description: `Get in touch with ${storeName}. Find our contact information, business hours, and send us a message for customer support and inquiries.`,
    keywords: 'contact us, customer support, phone number, email, business hours, help',
  };
}