import { StaticPageLayout } from '@/components/store/static-pages/StaticPageLayout';
import { Typography, Box, Grid, Card, CardContent, Avatar } from '@mui/material';
import { 
  AssignmentReturn, 
  Schedule, 
  LocalShipping, 
  CreditCard,
  CheckCircle,
  Cancel,
  Info
} from '@mui/icons-material';

interface RefundPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function RefundPolicyPage({ params }: RefundPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <StaticPageLayout title="Refund Policy" lastUpdated="2024-01-15T00:00:00Z">
      <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
        Refund and Return Policy
      </Typography>
      
      <Box className="info-box">
        <Typography variant="body1">
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        At {storeName}, we want you to be completely satisfied with your purchase. This Refund Policy 
        outlines the terms and conditions for returns, exchanges, and refunds. Please read this policy 
        carefully before making a purchase.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 3 }} fontWeight={600}>
        Return Policy Overview
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <Schedule />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  30-Day Return Window
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                You have 30 days from the date of delivery to initiate a return for most items.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <CheckCircle />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Original Condition
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Items must be in original, unused condition with all tags and packaging intact.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <LocalShipping />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Free Return Shipping
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                We provide prepaid return labels for eligible returns within our standard policy.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <CreditCard />
                </Avatar>
                <Typography variant="h6" fontWeight={600}>
                  Quick Refunds
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Refunds are processed within 5-7 business days after we receive your return.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Eligible Items for Return
      </Typography>
      
      <Typography variant="body1" paragraph>
        Most items purchased from {storeName} are eligible for return within 30 days of delivery, 
        provided they meet our return conditions:
      </Typography>
      
      <ul>
        <li>
          <Typography variant="body1" component="span">
            Items must be in original, unused condition
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            All original tags, labels, and packaging must be included
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Items must not show signs of wear, damage, or alteration
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            Original receipt or proof of purchase is required
          </Typography>
        </li>
      </ul>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Non-Returnable Items
      </Typography>
      
      <Box className="warning-box">
        <Typography variant="body1" paragraph>
          <strong>The following items cannot be returned:</strong>
        </Typography>
        
        <ul>
          <li>
            <Typography variant="body1" component="span">
              Personalized or customized items
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Perishable goods (food, flowers, etc.)
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Intimate or sanitary goods
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Hazardous materials or flammable liquids
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Digital downloads or software
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Gift cards or vouchers
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              Items returned after 30 days from delivery
            </Typography>
          </li>
        </ul>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        How to Initiate a Return
      </Typography>
      
      <Typography variant="body1" paragraph>
        To start a return, please follow these simple steps:
      </Typography>
      
      <ol>
        <li>
          <Typography variant="body1" component="span">
            <strong>Contact Us:</strong> Email us at returns@{storeHandle}.com or call +**************** 
            with your order number and reason for return.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Receive Return Authorization:</strong> We'll provide you with a Return Merchandise 
            Authorization (RMA) number and return instructions.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Package Your Items:</strong> Securely package the items in their original packaging 
            with all accessories and documentation.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Ship Your Return:</strong> Use the prepaid return label we provide and drop off 
            at any authorized shipping location.
          </Typography>
        </li>
        <li>
          <Typography variant="body1" component="span">
            <strong>Receive Your Refund:</strong> Once we receive and inspect your return, we'll 
            process your refund within 5-7 business days.
          </Typography>
        </li>
      </ol>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Refund Processing
      </Typography>
      
      <Typography variant="body1" paragraph>
        Once your return is received and inspected, we will send you an email to notify you that 
        we have received your returned item. We will also notify you of the approval or rejection 
        of your refund.
      </Typography>
      
      <Typography variant="body1" paragraph>
        If your refund is approved, it will be processed and a credit will automatically be applied 
        to your original method of payment within 5-7 business days.
      </Typography>

      <Box className="highlight-box" sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom fontWeight={600}>
          Refund Timeline by Payment Method
        </Typography>
        <ul>
          <li>
            <Typography variant="body1" component="span">
              <strong>Credit/Debit Cards:</strong> 3-5 business days
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>PayPal:</strong> 1-2 business days
            </Typography>
          </li>
          <li>
            <Typography variant="body1" component="span">
              <strong>Bank Transfer:</strong> 5-7 business days
            </Typography>
          </li>
        </ul>
      </Box>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Exchanges
      </Typography>
      
      <Typography variant="body1" paragraph>
        We offer exchanges for defective or damaged items. If you need to exchange an item for the 
        same product, please contact us at returns@{storeHandle}.com and we will provide instructions 
        for the exchange process.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Damaged or Defective Items
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you receive a damaged or defective item, please contact us immediately at 
        support@{storeHandle}.com with photos of the damage. We will arrange for a replacement 
        or full refund at no cost to you.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Late or Missing Refunds
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you haven't received a refund yet, first check your bank account again. Then contact 
        your credit card company or bank, as it may take some time before your refund is officially posted.
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you've done all of this and you still have not received your refund, please contact us 
        at refunds@{storeHandle}.com.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Shipping Costs
      </Typography>
      
      <Typography variant="body1" paragraph>
        We provide free return shipping for eligible returns within our standard policy. However, 
        original shipping costs are non-refundable unless the return is due to our error (wrong item 
        sent, defective product, etc.).
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        International Returns
      </Typography>
      
      <Typography variant="body1" paragraph>
        For international orders, customers are responsible for return shipping costs unless the 
        return is due to our error. Please contact us before returning international orders to 
        discuss the best return method.
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }} fontWeight={600}>
        Contact Information
      </Typography>
      
      <Typography variant="body1" paragraph>
        If you have any questions about our refund policy, please contact us:
      </Typography>
      
      <Box sx={{ ml: 2 }}>
        <Typography variant="body1" paragraph>
          <strong>Returns Email:</strong> returns@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </Typography>
      </Box>

      <Box className="info-box" sx={{ mt: 4 }}>
        <Typography variant="body1">
          <strong>Note:</strong> This refund policy is subject to change without notice. Any changes 
          will be posted on this page with an updated effective date. Your continued use of our 
          services after any changes constitutes acceptance of the new policy.
        </Typography>
      </Box>
    </StaticPageLayout>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: RefundPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Refund Policy | ${storeName}`,
    description: `Learn about ${storeName}'s refund and return policy. Understand our 30-day return window, eligible items, and refund process.`,
    keywords: 'refund policy, return policy, returns, refunds, exchange, money back guarantee',
  };
}