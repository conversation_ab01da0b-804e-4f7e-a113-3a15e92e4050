"use client";

import { ReactNode, useEffect } from "react";
import ReactQueryProvider from "./providers/react-query-provider";
import MuiThemeProvider from "./providers/mui-theme-provider";
import ToastProvider from "./providers/toast-provider";
import { GlobalLoadingProvider } from "@/components/loading/GlobalLoadingProvider";
import { LoadingOverlay } from "@/components/loading/LoadingOverlay";
import { LoadingDebug } from "@/components/debug/LoadingDebug";
import { useAuthStore } from "@/stores/authStore";
import { setMedusaToken } from "@/lib/api/medusa";

export const Providers = ({ children }: { children: ReactNode }) => {
  const token = useAuthStore((state) => state.token);
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const loadFromStorage = useAuthStore((state) => state.loadFromStorage);

  useEffect(() => {
    // Load auth state from localStorage on app startup
    console.log('=== PROVIDERS INITIALIZING ===');
    loadFromStorage();
    
    // Log current state after loading
    setTimeout(() => {
      console.log('Auth state after initialization:', {
        token: token ? 'exists' : 'null',
        user: user ? 'exists' : 'null',
        isAuthenticated
      });
    }, 100);
  }, [loadFromStorage, token, user, isAuthenticated]);

  useEffect(() => {
    if (token) {
      setMedusaToken(token);
    }
  }, [token]);

  return (
    <GlobalLoadingProvider>
      <MuiThemeProvider>
        <ToastProvider>
          <ReactQueryProvider>
            {children}
            <LoadingOverlay />
            <LoadingDebug />
          </ReactQueryProvider>
        </ToastProvider>
      </MuiThemeProvider>
    </GlobalLoadingProvider>
  );
};
